# IMU 數據轉發系統完整說明

## 系統概述

IMU 數據轄發系統是 6DIMU Sensor Tools 的核心功能之一，位於實時監控頁面的"數據管理"區塊中。該系統能夠將解析後的 IMU 數據通過多種網路協議即時轉發到外部系統，支援分 MAC 地址獨立發送，確保數據的完整性與獨立性。

## 核心特性

- **🚀 即時轉發**：數據接收後立即轉發，延遲極低（< 10ms）
- **🔄 多協議支援**：UDP、TCP、WebSocket、MQTT 四種主流協議
- **📡 分 MAC 轉發**：每個 MAC 地址的數據獨立發送，不會混合
- **📊 標準格式**：採用 JSON 格式，易於第三方系統集成
- **⚡ 高性能**：採用多線程架構，支援高頻數據轉發
- **🛡️ 錯誤處理**：完善的錯誤處理與自動重連機制

## 支援的網路協議

### 1. UDP（用戶數據報協議）
- **特點**：低延遲、高效率、無連接
- **適用場景**：實時數據流、高頻數據傳輸
- **優勢**：延遲最低，適合即時監控
- **注意**：不保證數據送達，適合容錯性高的應用

### 2. TCP（傳輸控制協議）
- **特點**：可靠傳輸、有連接、數據完整性保證
- **適用場景**：需要確保數據完整性的應用
- **優勢**：數據可靠性高，適合重要數據傳輸
- **注意**：延遲略高於 UDP，但數據更可靠

### 3. WebSocket
- **特點**：全雙工通信、Web 友好、低延遲
- **適用場景**：Web 應用集成、瀏覽器端接收
- **優勢**：適合 Web 開發，支援雙向通信
- **依賴**：需要安裝 `websocket-client` 套件

### 4. MQTT（物聯網消息協議）
- **特點**：發布/訂閱模式、輕量級、支援 QoS
- **適用場景**：物聯網平台、雲端服務集成
- **優勢**：支援多訂閱者、適合分散式系統
- **依賴**：需要安裝 `paho-mqtt` 套件

## 數據格式規範

### JSON 數據結構

轉發的數據採用標準 JSON 格式，確保跨平台兼容性與易於解析：

```json
{
  "timestamp": 1234567890123,           // 毫秒級時間戳
  "mac": "AA:BB:CC:DD:EE:FF",          // 感測器 MAC 地址
  "data": {
    // 6軸原始數據（已轉換為實際物理單位）
    "ax": 0.123456,                     // 加速度 X 軸 (g)
    "ay": 0.456789,                     // 加速度 Y 軸 (g)
    "az": 0.789012,                     // 加速度 Z 軸 (g)
    "gx": 1.234567,                     // 角速度 X 軸 (°/s)
    "gy": 2.345678,                     // 角速度 Y 軸 (°/s)
    "gz": 3.456789,                     // 角速度 Z 軸 (°/s)

    // Fusion 姿態數據
    "roll": 10.5,                       // 橫滾角 (°)
    "pitch": 20.3,                      // 俯仰角 (°)
    "yaw": 30.7,                        // 偏航角 (°)

    // 四元數（自動計算）
    "q0": 0.707107,                     // 四元數 W 分量
    "q1": 0.0,                          // 四元數 X 分量
    "q2": 0.0,                          // 四元數 Y 分量
    "q3": 0.707107                      // 四元數 Z 分量
  },
  "battery": 85                         // 電池電量 (0-100%)
}
```

### 數據精度與範圍

| 數據類型 | 精度 | 範圍 | 單位 |
|---------|------|------|------|
| 加速度 | 6位小數 | ±16g | g |
| 角速度 | 6位小數 | ±2000°/s | °/s |
| 歐拉角 | 1位小數 | ±180° | ° |
| 四元數 | 6位小數 | ±1.0 | 無單位 |
| 時間戳 | 毫秒 | Unix時間戳 | ms |
| 電池 | 整數 | 0-100 | % |

### 數據完整性保證

- **完整封包**：每個 JSON 封包包含所有感測器數據
- **時間同步**：所有數據共享同一時間戳
- **數據驗證**：發送前進行數據有效性檢查
- **編碼標準**：使用 UTF-8 編碼，確保國際化支援

## 詳細使用指南

### 1. 轉發參數配置

在實時監控頁面的"數據管理"區塊中進行配置：

#### 基本參數
- **協議選擇**：從下拉選單選擇 UDP、TCP、WebSocket 或 MQTT
- **目標地址**：輸入目標 IP 地址（預設：127.0.0.1）
- **目標端口**：輸入目標端口號（預設：8888）

#### 進階參數（依協議而定）
- **MQTT 主題**：設定 MQTT 發布主題（預設：imu/data）
- **WebSocket 路徑**：設定 WebSocket 連接路徑
- **連接超時**：設定連接超時時間（秒）
- **重試次數**：設定連接失敗時的重試次數

### 2. 啟動轉發流程

#### 步驟 1：檢查網路連接
確保目標系統可達且端口開放

#### 步驟 2：配置參數
1. 選擇適合的協議
2. 輸入正確的目標地址和端口
3. 根據需要調整進階參數

#### 步驟 3：啟動轉發
1. 點擊"啟動轉發"按鈕
2. 系統會嘗試建立連接
3. 狀態標籤顯示"轉發中 (協議名稱)"
4. 開始即時轉發所有接收到的 IMU 數據

#### 步驟 4：監控狀態
- 觀察狀態指示器的變化
- 檢查是否有錯誤提示
- 確認目標系統正常接收數據

### 3. 停止轉發

#### 正常停止
點擊"停止轉發"按鈕，系統會：
1. 停止發送新數據
2. 清空待發送隊列
3. 關閉網路連接
4. 更新狀態顯示

#### 異常停止
如遇到網路錯誤或目標系統無響應：
1. 系統會自動停止轉發
2. 彈出錯誤提示對話框
3. 狀態重置為"未啟動"
4. 可重新配置後再次啟動

## 測試工具與驗證

### 內建測試工具

專案提供了完整的測試工具 `test_data_receiver.py`，支援多種協議的數據接收測試：

#### 啟動測試工具
```bash
python test_data_receiver.py
```

#### 功能特性
- **多協議支援**：同時支援 UDP 和 TCP 接收
- **即時顯示**：即時顯示接收到的 IMU 數據
- **數據解析**：自動解析 JSON 格式的數據
- **統計信息**：顯示接收頻率和數據統計
- **錯誤處理**：完善的錯誤處理與提示

#### 測試輸出範例
```
[14:30:25] MAC: AA:BB:CC:DD:EE:FF | 加速度: (0.123, 0.456, 0.789)g | 姿態: R10.5° P20.3° Y30.7° | 電量: 85%
[14:30:25] MAC: 11:22:33:44:55:66 | 加速度: (0.234, 0.567, 0.890)g | 姿態: R15.2° P25.8° Y35.1° | 電量: 92%
```

### 第三方測試工具

#### UDP 測試
```bash
# 使用 netcat 測試 UDP 接收
nc -u -l 8888

# 使用 socat 測試 UDP 接收
socat UDP-LISTEN:8888,fork -
```

#### TCP 測試
```bash
# 使用 netcat 測試 TCP 接收
nc -l 8888

# 使用 telnet 測試 TCP 接收
telnet localhost 8888
```

#### WebSocket 測試
可使用瀏覽器開發者工具或 WebSocket 測試工具進行測試。

#### MQTT 測試
```bash
# 使用 mosquitto 客戶端訂閱
mosquitto_sub -h localhost -p 8888 -t "imu/data"
```

## 依賴套件安裝

### 核心依賴（必需）
```bash
pip install PyQt6 numpy
```

### 可選依賴

#### WebSocket 支援
```bash
pip install websocket-client
```

#### MQTT 支援
```bash
pip install paho-mqtt
```

#### 完整安裝（推薦）
```bash
pip install PyQt6 numpy websocket-client paho-mqtt
```

### 依賴版本需求
- **Python**: 3.8+
- **PyQt6**: 6.0+
- **numpy**: 1.20+
- **websocket-client**: 1.0+
- **paho-mqtt**: 1.5+

## 系統特性與優勢

### 核心特性
1. **🎯 分 MAC 轉發**：每個 MAC 地址的數據獨立發送，確保數據隔離
2. **⚡ 超低延遲**：數據接收後立即轉發，端到端延遲 < 10ms
3. **📦 完整數據**：包含所有解析後的 IMU 數據、姿態信息和電池狀態
4. **🔄 標準格式**：採用 JSON 格式，跨平台兼容，易於解析和集成
5. **⏰ 精確時間戳**：每個數據包都包含毫秒級 Unix 時間戳
6. **🔧 多線程架構**：採用生產者-消費者模式，確保高性能
7. **🛡️ 錯誤恢復**：完善的錯誤處理與自動重連機制

### 性能指標
- **轉發延遲**：< 10ms（本地網路環境）
- **支援頻率**：最高 1000Hz 數據轉發
- **並發連接**：支援多個目標同時轉發
- **數據完整性**：99.9% 數據完整性保證（TCP/WebSocket）
- **記憶體使用**：< 50MB 額外記憶體佔用

## 應用場景與整合

### 🏥 醫療與康復
- **遠程監控**：將患者運動數據即時傳送到醫療中心
- **康復評估**：整合到康復評估系統進行即時分析
- **數據記錄**：長期健康數據收集與分析
- **多點監控**：同時監控多個患者的康復進度

### 🏭 工業與研究
- **品質控制**：製造過程中的振動與運動監控
- **設備診斷**：機械設備的運行狀態監控
- **研究數據**：學術研究的數據收集與分析
- **自動化系統**：整合到工業自動化控制系統

### 🌐 物聯網與雲端
- **IoT 平台**：通過 MQTT 整合到物聯網平台
- **雲端分析**：數據上傳到雲端進行大數據分析
- **邊緣計算**：本地邊緣設備的數據處理
- **分散式系統**：多節點分散式數據收集

### 💻 軟體開發與整合
- **Web 應用**：通過 WebSocket 整合到網頁應用
- **移動應用**：整合到 iOS/Android 應用程式
- **數據分析**：整合到 Python/R/MATLAB 分析環境
- **機器學習**：為 ML 模型提供即時訓練數據

## 最佳實踐與建議

### 網路配置
1. **本地網路優先**：建議在本地網路環境下使用以確保穩定性
2. **防火牆設定**：確保目標端口在防火牆中開放
3. **網路頻寬**：大量數據轉發時注意網路頻寬限制
4. **QoS 設定**：在企業網路中設定適當的 QoS 優先級

### 目標系統要求
1. **處理能力**：確保目標系統有足夠的處理能力
2. **緩衝區大小**：適當設定接收緩衝區大小
3. **錯誤處理**：目標系統應具備適當的錯誤處理機制
4. **數據驗證**：建議對接收的數據進行有效性驗證

### 故障排除
1. **連接測試**：使用 ping 或 telnet 測試目標地址可達性
2. **端口檢查**：確認目標端口未被其他程式佔用
3. **防火牆檢查**：檢查本機和目標系統的防火牆設定
4. **日誌分析**：查看控制台輸出獲取詳細錯誤信息
5. **網路監控**：使用網路監控工具檢查數據傳輸狀況

## 錯誤處理與診斷

### 常見錯誤類型
- **連接失敗**：目標地址不可達或端口關閉
- **發送失敗**：網路中斷或目標系統無響應
- **協議錯誤**：不支援的協議或參數錯誤
- **依賴缺失**：WebSocket 或 MQTT 依賴套件未安裝

### 自動處理機制
- **自動重試**：連接失敗時自動重試（最多 3 次）
- **優雅降級**：轉發失敗時不影響主程式運行
- **狀態恢復**：錯誤恢復後自動恢復轉發狀態
- **資源清理**：異常退出時自動清理網路資源

### 診斷工具
- **狀態指示器**：即時顯示轉發狀態
- **錯誤對話框**：詳細的錯誤信息提示
- **控制台日誌**：完整的操作與錯誤日誌
- **統計信息**：發送成功/失敗統計

---
