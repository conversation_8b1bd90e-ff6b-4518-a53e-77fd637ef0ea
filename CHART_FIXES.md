# 圖表顯示問題修復

## 已修復的問題

### 1. 加速度/角速度模式無曲線顯示
**問題**: 選擇"加速度"或"角速度"時看不到曲線
**修復**: 
- 修復了checkbox狀態變化時的信號處理
- 在模式切換時暫時阻止信號，避免重複觸發
- 強制更新visible_dict和曲線可見性

### 2. 歐拉角曲線位置錯誤
**問題**: 歐拉角曲線顯示在錯誤位置
**修復**:
- 修復了右軸的幾何位置設置
- 添加了視圖變化時的右軸更新
- 確保右軸與主視圖正確鏈接

### 3. 數據更新邏輯問題
**問題**: 不可見的曲線不會更新數據，導致模式切換時看不到曲線
**修復**:
- 修改了update_plot方法，總是更新所有曲線的數據
- 只有可見的曲線才參與Y軸範圍計算
- 確保模式切換時數據立即可見

### 4. 數據範圍和轉換
**修復**:
- 添加了詳細的數據轉換調試信息
- 修復了clear方法，確保右軸線條也被清除
- 改進了Y軸範圍自動調整
- 顯示右軸標籤（歐拉角度數）

## 測試方法

### 1. 運行快速測試腳本（推薦）
```bash
.venv\Scripts\activate.ps1
python quick_test.py
```
這個腳本提供：
- 手動按鈕控制測試不同模式
- 清晰的模式切換
- 即時反饋

### 2. 運行自動測試腳本
```bash
python test_plotter.py
```
這個腳本會：
- 自動生成測試數據
- 每300個數據點自動切換模式
- 顯示詳細調試信息

### 2. 檢查控制台輸出
應該看到以下調試信息：
```
[DEBUG] Plotter initialized with visible curves: ['ax', 'ay', 'az']
[DEBUG] Plotter received data: ['ax', 'ay', 'az', 'gx', 'gy', 'gz', 'roll', 'pitch', 'yaw']
[DEBUG] ax: raw=1500, converted=0.732
[DEBUG] roll: raw=8000, converted=43.945
[DEBUG] Mode changed, visible curves: ['gx', 'gy', 'gz']
[DEBUG] Right axis range: -45.00 to 45.00
```

### 3. 預期行為
- **加速度模式**: 應該看到ax, ay, az三條曲線
- **角速度模式**: 應該看到gx, gy, gz三條曲線  
- **歐拉角模式**: 應該看到roll, pitch, yaw三條曲線在正確位置

## 主要修復代碼

### 1. 右軸設置修復
```python
# 設置右軸的幾何位置
self.right_axis.setGeometry(self.plot_widget.getViewBox().sceneBoundingRect())
# 連接視圖變化事件
self.plot_widget.getViewBox().sigResized.connect(self._update_right_axis)
```

### 2. 模式切換修復
```python
def _on_mode_radio_changed(self):
    # 暫時阻止checkbox信號，避免重複觸發
    for checkbox in self.checkbox_map.values():
        checkbox.blockSignals(True)
    # ... 設置checkbox狀態 ...
    # 恢復checkbox信號
    for checkbox in self.checkbox_map.values():
        checkbox.blockSignals(False)
    # 強制更新
    self.update_plot()
```

### 3. 清除方法修復
```python
def clear(self):
    for key in self.data_buffers:
        self.data_buffers[key].clear()
        self.lines[key].setData([], [])
        # 同時清除右軸線條
        if key in self.right_lines:
            self.right_lines[key].setData([], [])
```

## 如果仍有問題

1. 檢查控制台是否有錯誤信息
2. 確認PyQtGraph版本兼容性
3. 運行test_plotter.py確認基本功能
4. 檢查數據是否正確接收（查看調試信息）
