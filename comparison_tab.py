# comparison_tab.py
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QGroupBox, QGridLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from modern_widgets import ModernButton, SidebarSection

class ComparisonTab(QWidget):
    """比較分析頁籤"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.loaded_files = []  # 儲存已載入的檔案
        self._setup_ui()
        # 添加示例數據用於UI測試
        self._populate_sample_data()
    
    def _setup_ui(self):
        """設置UI佈局"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # 左側控制面板
        control_panel = self._create_control_panel()
        main_layout.addWidget(control_panel, 1)
        
        # 右側顯示區域
        display_area = self._create_display_area()
        main_layout.addWidget(display_area, 3)
    
    def _create_control_panel(self):
        """創建左側控制面板"""
        panel = QWidget()
        panel.setMaximumWidth(300)
        layout = QVBoxLayout(panel)
        layout.setSpacing(15)
        
        # 檔案管理區域
        file_section = SidebarSection("檔案管理")
        file_layout = QVBoxLayout()
        
        # 檔案1
        file1_layout = QVBoxLayout()
        file1_layout.addWidget(QLabel("檔案1:"))
        self.file1_label = QLabel("未選擇檔案")
        self.file1_label.setWordWrap(True)
        self.file1_label.setStyleSheet("color: #666; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px; min-height: 40px;")
        file1_layout.addWidget(self.file1_label)
        
        self.load_file1_btn = ModernButton("選擇檔案1", "primary")
        file1_layout.addWidget(self.load_file1_btn)
        file_layout.addLayout(file1_layout)
        
        # 檔案2
        file2_layout = QVBoxLayout()
        file2_layout.addWidget(QLabel("檔案2:"))
        self.file2_label = QLabel("未選擇檔案")
        self.file2_label.setWordWrap(True)
        self.file2_label.setStyleSheet("color: #666; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 4px; min-height: 40px;")
        file2_layout.addWidget(self.file2_label)
        
        self.load_file2_btn = ModernButton("選擇檔案2", "success")
        file2_layout.addWidget(self.load_file2_btn)
        file_layout.addLayout(file2_layout)
        
        # 清除按鈕
        self.clear_files_btn = ModernButton("清除所有檔案", "danger")
        file_layout.addWidget(self.clear_files_btn)
        
        file_section.setLayout(file_layout)
        layout.addWidget(file_section)
        
        # 比較設置區域
        comparison_section = SidebarSection("比較設置")
        comparison_layout = QVBoxLayout()
        
        # 比較模式選擇
        mode_layout = QVBoxLayout()
        mode_layout.addWidget(QLabel("比較模式:"))
        
        self.mode_buttons = []
        modes = [
            ("整體比較", "比較整個運動過程的指標"),
            ("時間段比較", "比較特定時間段的表現"),
            ("峰值比較", "比較各項指標的峰值")
        ]
        
        for mode_name, description in modes:
            btn = ModernButton(mode_name, "primary")
            btn.setCheckable(True)
            btn.setToolTip(description)
            self.mode_buttons.append(btn)
            mode_layout.addWidget(btn)
        
        # 預設選擇第一個模式
        if self.mode_buttons:
            self.mode_buttons[0].setChecked(True)
        
        comparison_layout.addLayout(mode_layout)
        
        # 開始比較按鈕
        self.compare_btn = ModernButton("開始比較", "primary")
        self.compare_btn.setEnabled(False)  # 初始禁用
        comparison_layout.addWidget(self.compare_btn)
        
        comparison_section.setLayout(comparison_layout)
        layout.addWidget(comparison_section)
        
        # 導出區域
        export_section = SidebarSection("結果導出")
        export_layout = QVBoxLayout()
        
        self.export_report_btn = ModernButton("導出報告", "warning")
        self.export_report_btn.setEnabled(False)
        export_layout.addWidget(self.export_report_btn)
        
        self.export_chart_btn = ModernButton("導出圖表", "warning")
        self.export_chart_btn.setEnabled(False)
        export_layout.addWidget(self.export_chart_btn)
        
        export_section.setLayout(export_layout)
        layout.addWidget(export_section)
        
        layout.addStretch()
        return panel
    
    def _create_display_area(self):
        """創建右側顯示區域"""
        display = QWidget()
        layout = QVBoxLayout(display)
        layout.setSpacing(15)
        
        # 標題
        title = QLabel("比較分析結果")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setStyleSheet("color: #00d4ff; padding: 10px;")
        layout.addWidget(title)
        
        # 使用分割器分為上下兩部分
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部：比較表格
        table_group = QGroupBox("指標比較")
        table_layout = QVBoxLayout(table_group)
        
        self.comparison_table = QTableWidget()
        self.comparison_table.setColumnCount(4)
        self.comparison_table.setHorizontalHeaderLabels(["指標", "檔案1", "檔案2", "差異"])
        
        # 設置表格樣式
        self.comparison_table.setStyleSheet("""
            QTableWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid #3a3a4e;
                border-radius: 8px;
                gridline-color: #3a3a4e;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #3a3a4e;
            }
            QHeaderView::section {
                background: rgba(0, 212, 255, 0.1);
                color: #00d4ff;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # 設置表格屬性
        header = self.comparison_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.comparison_table.setAlternatingRowColors(True)
        self.comparison_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        table_layout.addWidget(self.comparison_table)
        splitter.addWidget(table_group)
        
        # 下半部：圖表顯示區域
        chart_group = QGroupBox("比較圖表")
        chart_layout = QVBoxLayout(chart_group)
        
        self.chart_placeholder = QLabel("比較圖表將在此顯示")
        self.chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.chart_placeholder.setStyleSheet("""
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed #666;
            border-radius: 8px;
            padding: 40px;
            color: #666;
            font-size: 14px;
        """)
        self.chart_placeholder.setMinimumHeight(250)
        chart_layout.addWidget(self.chart_placeholder)
        
        splitter.addWidget(chart_group)
        
        # 設置分割器比例
        splitter.setSizes([300, 250])
        layout.addWidget(splitter)
        
        return display
    
    def _populate_sample_data(self):
        """填充示例數據（用於UI測試）"""
        sample_data = [
            ["最大速度", "5.2 m/s", "4.8 m/s", "-7.7% ↓"],
            ["最大加速度", "12.3 m/s²", "13.1 m/s²", "+6.5% ↑"],
            ["最大力度", "2.1 g", "2.3 g", "+9.5% ↑"],
            ["運動時長", "45 秒", "52 秒", "+15.6% ↑"],
            ["平均速度", "2.1 m/s", "2.3 m/s", "+9.5% ↑"]
        ]
        
        self.comparison_table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                
                # 為差異列設置顏色
                if col == 3:  # 差異列
                    if "↑" in value:
                        item.setForeground(Qt.GlobalColor.green)
                    elif "↓" in value:
                        item.setForeground(Qt.GlobalColor.red)
                
                self.comparison_table.setItem(row, col, item)
    
    def update_file_status(self):
        """更新檔案狀態和按鈕可用性"""
        files_loaded = len(self.loaded_files) >= 2
        self.compare_btn.setEnabled(files_loaded)
        
        if files_loaded:
            self.compare_btn.setText("開始比較")
        else:
            self.compare_btn.setText(f"需要載入 {2 - len(self.loaded_files)} 個檔案")
