# PySide6 轉換修復說明

## 已修復的圖表顯示問題

### 1. PyQtGraph 兼容性設置
在所有主要檔案中添加了 PyQtGraph 使用 PySide6 的環境變數設置：
```python
import os
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'
```

### 2. 信號處理修復
- 修復了 `stateChanged` 信號在 PySide6 中的參數處理
- 在 `_on_legend_checkbox_changed` 方法中添加了對 Qt.CheckState 枚舉值的正確處理

### 3. 線條可見性同步
- 修復了左軸和右軸線條可見性不同步的問題
- 在 `set_curve_visibility` 和 `_on_legend_checkbox_changed` 方法中同時更新兩個軸的線條

### 4. 初始化修復
- 確保 `visible_dict` 與 checkbox 狀態在初始化時正確同步
- 強制設置所有線條的初始可見性狀態

### 5. 圖表更新優化
- 移除了重複的 `update_plot()` 調用
- 使用統一的定時器來更新所有圖表（60 FPS）

### 6. 調試信息
添加了詳細的調試信息來幫助診斷問題：
- 數據接收調試信息
- 圖表更新調試信息
- Checkbox 狀態變化調試信息
- 可見曲線列表調試信息

### 7. 測試工具
創建了 `test_plotter.py` 獨立測試腳本來驗證圖表功能。

## 使用方法

### 運行主程式
```bash
python main.py
```

### 測試圖表功能
```bash
python test_plotter.py
```

## 預期行為

1. **啟動時**: 應該看到加速度曲線（ax, ay, az）默認可見
2. **數據接收**: 當連接感測器並接收數據時，應該看到實時動態曲線
3. **模式切換**: 使用 radio button 可以切換顯示不同類型的數據
4. **Checkbox 控制**: 可以單獨控制每條曲線的顯示/隱藏

## 調試信息

程式運行時會在控制台輸出調試信息：
- `[DEBUG] Plotter initialized with visible curves: [...]`
- `[DEBUG] Plotter received data: [...]`
- `[DEBUG] Plot update - Buffers: {...}, Visible: [...]`
- `[DEBUG] Checkbox ... changed to ... (raw state: ...)`

如果看不到曲線，請檢查：
1. 控制台是否有調試信息
2. 是否有數據被接收
3. Checkbox 是否正確選中
4. 是否有錯誤信息

## 已知問題

1. 某些未使用的 import 會產生警告，但不影響功能
2. 如果仍然看不到曲線，可能需要檢查 PyQtGraph 版本兼容性

## 建議的測試步驟

1. 先運行 `test_plotter.py` 確認圖表基本功能正常
2. 再運行 `main.py` 測試完整應用程式
3. 連接感測器並檢查是否有實時數據顯示
