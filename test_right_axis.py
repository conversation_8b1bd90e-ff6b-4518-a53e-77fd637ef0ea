#!/usr/bin/env python3
"""
簡化的右軸測試腳本
專門測試歐拉角在右軸的顯示問題
"""
import sys
import os
import math

# 設置pyqtgraph使用PySide6
import os
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'
import pyqtgraph as pg

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
from PySide6.QtCore import QTimer
from plotter import Plotter

class RightAxisTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Right Axis Test")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 直接設置為歐拉角模式
        self.plotter.radio_euler.setChecked(True)
        
        # 創建定時器來模擬數據
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_euler_data)
        self.timer.start(100)  # 10 FPS
        
        self.counter = 0
        print("Right axis test started - Euler mode only")
        
    def generate_euler_data(self):
        """只生成歐拉角數據"""
        self.counter += 1
        t = self.counter * 0.1
        
        # 只生成歐拉角數據
        test_data = {
            'roll': int(math.sin(t * 0.3) * 8000),   # ±44°
            'pitch': int(math.cos(t * 0.4) * 6000),  # ±33°
            'yaw': int(math.sin(t * 0.2) * 10000)    # ±55°
        }
        
        # 添加數據到plotter
        self.plotter.add_data(test_data)
        
        # 每50次打印一次調試信息
        if self.counter % 50 == 0:
            print(f"Generated {self.counter} euler data points")
            roll_converted = test_data['roll'] * 180.0 / 32768.0
            pitch_converted = test_data['pitch'] * 180.0 / 32768.0
            yaw_converted = test_data['yaw'] * 180.0 / 32768.0
            print(f"Converted: roll={roll_converted:.1f}°, pitch={pitch_converted:.1f}°, yaw={yaw_converted:.1f}°")
            
            # 檢查右軸線條狀態
            for key in ['roll', 'pitch', 'yaw']:
                if key in self.plotter.right_lines:
                    line = self.plotter.right_lines[key]
                    visible = line.isVisible()
                    data_len = len(line.xData) if hasattr(line, 'xData') and line.xData is not None else 0
                    print(f"  {key}: visible={visible}, data_points={data_len}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = RightAxisTest()
    window.show()
    sys.exit(app.exec())
