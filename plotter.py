import pyqtgraph as pg
from collections import deque
from PySide6.QtWidgets import QRadioButton, QButtonGroup

# 設置pyqtgraph使用PySide6
import os
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'

# 現在導入pyqtgraph的Qt模組
from pyqtgraph.Qt import QtWidgets

class Plotter:
    def __init__(self, figsize=(3,2), dpi=100):
        self.data_length = 500
        self.data_buffers = {}
        self.lines = {}
        self.colors = {
            'ax': 'blue', 'ay': 'green', 'az': 'red',
            'gx': 'magenta', 'gy': 'orange', 'gz': 'brown',
            'roll': 'navy', 'pitch': 'darkorange', 'yaw': 'purple',
        }
        # 預設全部曲線可見
        self.visible_dict = {k: True for k in self.colors}
        # 主Widget: 嵌入一個垂直layout，圖表在上，圖例在下
        self.main_widget = QtWidgets.QWidget()
        self.main_layout = QtWidgets.QVBoxLayout(self.main_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(2)
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground('w')
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.setTitle('IMU Data', color='k', size='10pt')
        self.y_unit_left = 'g, °/s'
        self.y_unit_right = '°'
        self.plot_widget.setLabel('left', f'IMU Value ({self.y_unit_left})', color='k')
        self.plot_widget.setLabel('bottom', 'Samples', color='k')
        self.plot_widget.showAxis('right')
        self.plot_widget.setLabel('right', f'Euler Value ({self.y_unit_right})', color='k')
        self.main_layout.addWidget(self.plot_widget)
        # 雙Y軸
        self.right_axis = pg.ViewBox()
        self.plot_widget.scene().addItem(self.right_axis)
        self.plot_widget.getAxis('right').linkToView(self.right_axis)
        self.right_axis.setXLink(self.plot_widget)
        # 顯示右軸
        self.plot_widget.showAxis('right')
        self.plot_widget.getAxis('right').setLabel('Euler Angle (°)')
        # 連接視圖變化事件
        self.plot_widget.getViewBox().sigResized.connect(self._update_right_axis)
        self.right_lines = {}
        print("[DEBUG] Right axis initialized")
        # 創建自定義圖例
        self.legend_layout = QtWidgets.QHBoxLayout()
        self.legend_layout.setSpacing(5)
        self.legend_layout.setContentsMargins(10, 0, 10, 0)
        self.legend_widget = QtWidgets.QWidget()
        self.legend_widget.setLayout(self.legend_layout)
        self.legend_widget.setStyleSheet("background-color: transparent;")
        self.main_layout.addWidget(self.legend_widget)
        # 新增數據顯示模式選擇
        self.mode_group = QButtonGroup(self.main_widget)
        self.radio_acc = QRadioButton('加速度')
        self.radio_gyro = QRadioButton('角速度')
        self.radio_euler = QRadioButton('歐拉角')
        self.mode_group.addButton(self.radio_acc)
        self.mode_group.addButton(self.radio_gyro)
        self.mode_group.addButton(self.radio_euler)
        mode_layout = QtWidgets.QHBoxLayout()
        mode_layout.addWidget(self.radio_acc)
        mode_layout.addWidget(self.radio_gyro)
        mode_layout.addWidget(self.radio_euler)
        self.main_layout.insertLayout(0, mode_layout)
        self.radio_acc.toggled.connect(self._on_mode_radio_changed)
        self.radio_gyro.toggled.connect(self._on_mode_radio_changed)
        self.radio_euler.toggled.connect(self._on_mode_radio_changed)
        # 為每個數據創建圖例項
        checkbox_style = """
            QCheckBox {
                background-color: transparent;
                padding: 0px;
                margin: 0px;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
                border: 1px solid gray;
                border-radius: 2px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #2196F3;
                border-color: #2196F3;
            }
            QCheckBox::indicator:hover {
                border-color: #2196F3;
            }
        """
        self.checkbox_map = {}  # key:checkbox
        for key, color in self.colors.items():
            item_widget = QtWidgets.QWidget()
            item_layout = QtWidgets.QHBoxLayout()
            item_layout.setSpacing(2)
            item_layout.setContentsMargins(0, 0, 0, 0)
            checkbox = QtWidgets.QCheckBox()
            # 預設只開啟加速度三軸
            if key in ['ax','ay','az']:
                checkbox.setChecked(True)
            else:
                checkbox.setChecked(False)
            checkbox.setStyleSheet(checkbox_style)
            label = QtWidgets.QLabel(key)
            label.setStyleSheet(f"color: {color}; background-color: transparent; padding: 0px; margin: 0px; font-size: 8pt;")
            label.setFixedHeight(15)
            item_layout.addWidget(checkbox)
            item_layout.addWidget(label)
            item_widget.setLayout(item_layout)
            self.legend_layout.addWidget(item_widget)
            checkbox.stateChanged.connect(lambda state, k=key: self._on_legend_checkbox_changed(k, state))
            self.lines[key] = self.plot_widget.plot([], [], pen=pg.mkPen(color=color, width=2))
            # 根據checkbox狀態設置線條可見性
            is_visible = checkbox.isChecked()
            self.lines[key].setVisible(is_visible)
            if key in ['roll','pitch','yaw']:
                self.right_lines[key] = pg.PlotCurveItem(pen=pg.mkPen(color=color, width=2))
                self.right_lines[key].setVisible(is_visible)
                self.right_axis.addItem(self.right_lines[key])
            self.checkbox_map[key] = checkbox
        self.plot_widget.setXRange(0, self.data_length)
        for key in self.colors:
            self.data_buffers[key] = deque(maxlen=self.data_length)
        # 初始化visible_dict與checkbox狀態同步
        self.visible_dict = {k: self.checkbox_map[k].isChecked() for k in self.colors}
        # 預設radio button選加速度，並同步checkbox
        self.radio_acc.setChecked(True)
        self._on_mode_radio_changed()

        # 注意：_on_mode_radio_changed()已經設置了可見性，不需要重複設置

        # 延遲更新右軸，確保widget完全初始化
        QtWidgets.QApplication.processEvents()
        self._update_right_axis()

        print(f"[DEBUG] Plotter initialized with visible curves: {[k for k, v in self.visible_dict.items() if v]}")
        print(f"[DEBUG] Right axis lines: {list(self.right_lines.keys())}")

    def get_canvas(self):
        return self.main_widget

    def update_plot_unit(self):
        """根據當前模式更新Y軸單位"""
        if hasattr(self, 'cur_mode') and self.cur_mode == 'fusion':
            self.y_unit = '°'  # 歐拉角單位
        else:
            self.y_unit = 'g or °/s'    # 加速度和角速度單位
        self.plot_widget.setLabel('left', f'Value ({self.y_unit})')

    def add_data(self, data_dict):
        # 添加調試信息
        if hasattr(self, '_debug_counter'):
            self._debug_counter += 1
        else:
            self._debug_counter = 1

        # 每100次數據更新打印一次調試信息
        if self._debug_counter % 100 == 0:
            print(f"[DEBUG] Plotter received data: {list(data_dict.keys())}")
            # 打印一些樣本數據值
            sample_data = {k: v for k, v in data_dict.items() if k in ['ax', 'roll']}
            print(f"[DEBUG] Sample raw data: {sample_data}")

        for key, value in data_dict.items():
            if key not in self.colors:
                continue  # 忽略非曲線顯示欄位（如q0~q3）
            if not isinstance(value, (int, float)):
                continue
            if key in ['ax', 'ay', 'az']:
                converted_value = value * 16.0 / 32768.0
            elif key in ['gx', 'gy', 'gz']:
                converted_value = value * 2000.0 / 32768.0
            elif key in ['roll', 'pitch', 'yaw']:
                converted_value = value * 180.0 / 32768.0
            else:
                converted_value = value
            self.data_buffers.setdefault(key, deque(maxlen=self.data_length)).append(converted_value)

            # 前10次或每100次打印轉換後的數據範例
            if (self._debug_counter <= 10 or self._debug_counter % 100 == 0) and key in ['ax', 'roll']:
                print(f"[DEBUG] {key}: raw={value}, converted={converted_value:.3f}")

        # 重要：添加數據後必須更新圖表！
        self.update_plot()

    def update_plot(self):
        min_y_left, max_y_left = None, None
        min_y_right, max_y_right = None, None

        # 添加調試信息
        if hasattr(self, '_plot_debug_counter'):
            self._plot_debug_counter += 1
        else:
            self._plot_debug_counter = 1

        # 每次都打印調試信息（前10次）
        if self._plot_debug_counter <= 10:
            print(f"[DEBUG] update_plot called #{self._plot_debug_counter}")
            buffer_info = {k: len(v) for k, v in self.data_buffers.items() if len(v) > 0}
            visible_info = {k: v for k, v in self.visible_dict.items() if v}
            print(f"[DEBUG] Buffers with data: {buffer_info}")
            print(f"[DEBUG] Visible curves: {list(visible_info.keys())}")

        # 每200次更新打印一次調試信息
        if self._plot_debug_counter % 200 == 0:
            buffer_info = {k: len(v) for k, v in self.data_buffers.items() if len(v) > 0}
            visible_info = {k: v for k, v in self.visible_dict.items() if v}
            print(f"[DEBUG] Plot update - Buffers: {buffer_info}, Visible: {list(visible_info.keys())}")

        for key, buffer in self.data_buffers.items():
            # 總是更新數據，但只有可見的曲線才計算範圍
            if len(buffer) > 0:
                x = list(range(len(buffer)))
                y = list(buffer)
                if key in ['roll','pitch','yaw']:
                    self.right_lines[key].setData(x, y)
                    # 只有可見的曲線才計算範圍
                    if self.visible_dict.get(key, False):
                        cur_min = min(y)
                        cur_max = max(y)
                        min_y_right = cur_min if min_y_right is None else min(min_y_right, cur_min)
                        max_y_right = cur_max if max_y_right is None else max(max_y_right, cur_max)
                else:
                    self.lines[key].setData(x, y)
                    # 調試信息（前10次）
                    if self._plot_debug_counter <= 10:
                        visible = self.visible_dict.get(key, False)
                        print(f"[DEBUG] Set {key} data: {len(x)} points, visible={visible}, range={min(y):.2f} to {max(y):.2f}")
                    # 只有可見的曲線才計算範圍
                    if self.visible_dict.get(key, False):
                        cur_min = min(y)
                        cur_max = max(y)
                        min_y_left = cur_min if min_y_left is None else min(min_y_left, cur_min)
                        max_y_left = cur_max if max_y_left is None else max(max_y_left, cur_max)
            else:
                # 沒有數據時清空曲線
                if key in ['roll','pitch','yaw']:
                    self.right_lines[key].setData([],[])
                else:
                    self.lines[key].setData([],[])

        # 自動調整Y軸範圍
        if min_y_left is not None and max_y_left is not None:
            margin = (max_y_left - min_y_left) * 0.1
            if margin == 0:
                margin = abs(min_y_left) * 0.1 if min_y_left != 0 else 0.1
            y_min = min_y_left - margin
            y_max = max_y_left + margin
            self.plot_widget.setYRange(y_min, y_max)

            # 調試信息（前10次）
            if self._plot_debug_counter <= 10:
                print(f"[DEBUG] Left axis range set: {y_min:.3f} to {y_max:.3f}")
        else:
            # 沒有可見的左軸數據時，設置默認範圍
            self.plot_widget.setYRange(-10, 10)
            if self._plot_debug_counter <= 10:
                print(f"[DEBUG] Left axis set to default range: -10 to 10")

        if min_y_right is not None and max_y_right is not None:
            margin = (max_y_right - min_y_right) * 0.1
            if margin == 0:
                margin = abs(min_y_right) * 0.1 if min_y_right != 0 else 0.1
            y_min = min_y_right - margin
            y_max = max_y_right + margin
            self.right_axis.setYRange(y_min, y_max)

            # 調試信息（前10次）
            if self._plot_debug_counter <= 10:
                print(f"[DEBUG] Right axis range set: {y_min:.3f} to {y_max:.3f}")
        else:
            # 沒有可見的右軸數據時，設置默認範圍
            self.right_axis.setYRange(-180, 180)
            if self._plot_debug_counter <= 10:
                print(f"[DEBUG] Right axis set to default range: -180 to 180")

        self.plot_widget.setXRange(0, self.data_length)

        # 確保右軸位置正確
        self._update_right_axis()

    def set_curve_visibility(self, visible_dict, force_fusion_only=False):
        if force_fusion_only:
            visible_dict = {k: (k in ['roll','pitch','yaw']) for k in self.lines}
        self.visible_dict = visible_dict
        for key, line in self.lines.items():
            is_visible = self.visible_dict.get(key, False)
            line.setVisible(is_visible)
            print(f"[DEBUG] Set {key} visibility to {is_visible}")
        # 同時更新右軸線條的可見性
        for key, line in self.right_lines.items():
            is_visible = self.visible_dict.get(key, False)
            line.setVisible(is_visible)
            print(f"[DEBUG] Set right axis {key} visibility to {is_visible}")

    def set_axis_unit(self, unit):
        self.y_unit = unit
        self.plot_widget.setLabel('left', f'Value ({self.y_unit})')

    def _on_legend_checkbox_changed(self, key, state):
        # 在PySide6中，state可能是Qt.CheckState枚舉值
        is_checked = bool(state) if isinstance(state, bool) else (state == 2)  # Qt.Checked = 2
        self.visible_dict[key] = is_checked
        self.lines[key].setVisible(is_checked)
        # 同時更新右軸線條的可見性（歐拉角）
        if key in self.right_lines:
            self.right_lines[key].setVisible(is_checked)

        # 添加調試信息
        print(f"[DEBUG] Checkbox {key} changed to {is_checked} (raw state: {state})")
        
    def show_large_plot(self, parent=None):
        dlg = QtWidgets.QDialog(parent)
        dlg.setWindowTitle("放大圖")
        vbox = QtWidgets.QVBoxLayout(dlg)
        big_plot = pg.PlotWidget()
        
        # 使用與主圖表相同的背景色
        background_color = self.plot_widget.background.color().name()
        text_color = 'k' if background_color == '#ffffff' else 'w'
        big_plot.setBackground(background_color)
        big_plot.showGrid(x=True, y=True)
        big_plot.setTitle('IMU Data', color=text_color, size='12pt')
        big_plot.setLabel('left', f'Value ({self.y_unit})', color=text_color)
        big_plot.setLabel('bottom', 'Samples', color=text_color)
        
        # 為大圖創建圖例
        legend_layout = QtWidgets.QHBoxLayout()
        legend_layout.setSpacing(10)
        legend_widget = QtWidgets.QWidget()
        legend_widget.setLayout(legend_layout)
        legend_widget.setStyleSheet("background-color: transparent;")
        
        # 只顯示當前可見的數據
        for key, color in self.colors.items():
            if self.visible_dict.get(key, True):
                # 繪製數據
                pen = pg.mkPen(color=color, width=2)
                data = list(self.data_buffers[key])
                if data:
                    big_plot.plot(list(range(len(data))), data, pen=pen)
                
                # 創建圖例項
                item_widget = QtWidgets.QWidget()
                item_layout = QtWidgets.QHBoxLayout()
                item_layout.setContentsMargins(0, 0, 0, 0)
                
                label = QtWidgets.QLabel(key)
                label.setStyleSheet(f"color: {color}; background-color: transparent;")
                
                item_layout.addWidget(label)
                item_widget.setLayout(item_layout)
                legend_layout.addWidget(item_widget)
        
        # 將圖例添加到場景
        proxy = big_plot.scene().addWidget(legend_widget)
        proxy.setPos(10, 10)
        
        big_plot.setYRange(-40000, 40000)
        big_plot.setXRange(0, self.data_length)
        
        vbox.addWidget(big_plot)
        dlg.setLayout(vbox)
        dlg.exec()

    def clear(self):
        for key in self.data_buffers:
            self.data_buffers[key].clear()
            self.lines[key].setData([], [])
            # 同時清除右軸線條
            if key in self.right_lines:
                self.right_lines[key].setData([], [])
        print("[DEBUG] Plotter cleared")

    def set_background(self, color):
        self.plot_widget.setBackground(color)
        # 根據背景色調整文字顏色
        text_color = 'k' if color == 'w' else 'w'
        self.plot_widget.setTitle('IMU Data', color=text_color, size='10pt')
        self.plot_widget.setLabel('left', f'Value ({self.y_unit})' if self.y_unit else 'Value', color=text_color)
        self.plot_widget.setLabel('bottom', 'Samples', color=text_color)
        
        # 更新所有圖例標籤的顏色
        for i in range(self.legend_layout.count()):
            item = self.legend_layout.itemAt(i).widget()
            if item:
                label = item.findChild(QtWidgets.QLabel)
                if label:
                    current_style = label.styleSheet()
                    color_start = current_style.find("color: ") + 7
                    color_end = current_style.find(";", color_start)
                    color_value = current_style[color_start:color_end]
                    new_style = current_style.replace(
                        f"color: {color_value}",
                        f"color: {color_value}"  # 保持原有顏色不變
                    )
                    label.setStyleSheet(new_style) 

    def _on_plot_resize(self, event):
        # 動態調整圖例位置，始終在右上角下方
        legend_widget = self.legend_layout.parentWidget()
        proxy = None
        for item in self.plot_widget.scene().items():
            if hasattr(item, 'widget') and item.widget() is legend_widget:
                proxy = item
                break
        if proxy:
            proxy.setPos(self.plot_widget.width() - legend_widget.width() - 20, 35)
        pg.PlotWidget.resizeEvent(self.plot_widget, event) 

    def _update_right_axis(self):
        """更新右軸的幾何位置和鏈接"""
        try:
            # 獲取主視圖的幾何位置
            main_view = self.plot_widget.getViewBox()
            if main_view and main_view.sceneBoundingRect().isValid():
                # 設置右軸與主視圖相同的幾何位置
                rect = main_view.sceneBoundingRect()
                self.right_axis.setGeometry(rect)
                # 確保X軸鏈接
                self.right_axis.setXLink(main_view)
                # 確保右軸可見
                self.right_axis.setVisible(True)
                if hasattr(self, '_debug_right_axis_counter'):
                    self._debug_right_axis_counter += 1
                else:
                    self._debug_right_axis_counter = 1
                if self._debug_right_axis_counter % 100 == 0:
                    print(f"[DEBUG] Right axis updated {self._debug_right_axis_counter} times, rect: {rect}")
        except Exception as e:
            print(f"[DEBUG] Error updating right axis: {e}")

    def _on_mode_radio_changed(self):
        # 暫時阻止checkbox信號，避免重複觸發
        for checkbox in self.checkbox_map.values():
            checkbox.blockSignals(True)

        if self.radio_acc.isChecked():
            # 只顯示加速度
            for k in self.colors:
                self.checkbox_map[k].setChecked(k in ['ax','ay','az'])
        elif self.radio_gyro.isChecked():
            for k in self.colors:
                self.checkbox_map[k].setChecked(k in ['gx','gy','gz'])
        elif self.radio_euler.isChecked():
            for k in self.colors:
                self.checkbox_map[k].setChecked(k in ['roll','pitch','yaw'])

        # 恢復checkbox信號
        for checkbox in self.checkbox_map.values():
            checkbox.blockSignals(False)

        # 強制更新visible_dict
        self.visible_dict = {k: self.checkbox_map[k].isChecked() for k in self.colors}

        # 強制刷新曲線顯示
        self.set_curve_visibility(self.visible_dict)

        print(f"[DEBUG] Mode changed, visible curves: {[k for k, v in self.visible_dict.items() if v]}")

        # 強制更新圖表
        self.update_plot()