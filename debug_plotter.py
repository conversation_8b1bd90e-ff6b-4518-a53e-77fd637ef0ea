#!/usr/bin/env python3
"""
調試plotter邏輯 - 不需要GUI
"""
import sys
import os
from collections import deque

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_plotter_logic():
    """調試plotter的數據處理邏輯"""
    print("=== Debugging Plotter Logic ===")
    
    # 模擬plotter的關鍵屬性
    colors = {
        'ax': '#1f77b4', 'ay': '#ff7f0e', 'az': '#2ca02c',
        'gx': '#d62728', 'gy': '#9467bd', 'gz': '#8c564b',
        'roll': '#e377c2', 'pitch': '#7f7f7f', 'yaw': '#bcbd22'
    }
    
    data_length = 500
    data_buffers = {}
    
    # 初始化數據緩衝區
    for key in colors:
        data_buffers[key] = deque(maxlen=data_length)
    
    print(f"Initialized buffers for: {list(colors.keys())}")
    
    # 模擬add_data邏輯
    test_data = {
        'ax': 16384 + 1000,  # 模擬加速度數據
        'ay': 16384 - 500,
        'az': 16384,
        'roll': 8000,        # 模擬歐拉角數據
        'pitch': -6000,
        'yaw': 10000
    }
    
    print(f"\nTest data: {test_data}")
    
    # 數據轉換和存儲
    for key, value in test_data.items():
        if key not in colors:
            continue
        if not isinstance(value, (int, float)):
            continue
            
        if key in ['ax', 'ay', 'az']:
            converted_value = value * 16.0 / 32768.0
        elif key in ['gx', 'gy', 'gz']:
            converted_value = value * 2000.0 / 32768.0
        elif key in ['roll', 'pitch', 'yaw']:
            converted_value = value * 180.0 / 32768.0
        else:
            converted_value = value
            
        data_buffers[key].append(converted_value)
        print(f"{key}: raw={value}, converted={converted_value:.3f}")
    
    # 檢查緩衝區狀態
    print(f"\nBuffer lengths:")
    for key, buffer in data_buffers.items():
        if len(buffer) > 0:
            print(f"  {key}: {len(buffer)} points, last value: {buffer[-1]:.3f}")
    
    # 模擬可見性設置
    print(f"\n=== Testing Visibility Logic ===")
    
    # 加速度模式
    acc_visible = {k: (k in ['ax','ay','az']) for k in colors}
    print(f"Acceleration mode visible: {[k for k, v in acc_visible.items() if v]}")
    
    # 角速度模式
    gyro_visible = {k: (k in ['gx','gy','gz']) for k in colors}
    print(f"Gyroscope mode visible: {[k for k, v in gyro_visible.items() if v]}")
    
    # 歐拉角模式
    euler_visible = {k: (k in ['roll','pitch','yaw']) for k in colors}
    print(f"Euler mode visible: {[k for k, v in euler_visible.items() if v]}")
    
    # 模擬update_plot邏輯
    print(f"\n=== Testing Update Plot Logic ===")
    
    for mode_name, visible_dict in [("Acceleration", acc_visible), ("Gyroscope", gyro_visible), ("Euler", euler_visible)]:
        print(f"\n{mode_name} mode:")
        min_y_left, max_y_left = None, None
        min_y_right, max_y_right = None, None
        
        for key, buffer in data_buffers.items():
            if len(buffer) > 0:
                y = list(buffer)
                if key in ['roll','pitch','yaw']:
                    # 右軸
                    if visible_dict.get(key, False):
                        cur_min = min(y)
                        cur_max = max(y)
                        min_y_right = cur_min if min_y_right is None else min(min_y_right, cur_min)
                        max_y_right = cur_max if max_y_right is None else max(max_y_right, cur_max)
                        print(f"  Right axis {key}: visible, range {cur_min:.2f} to {cur_max:.2f}")
                    else:
                        print(f"  Right axis {key}: hidden")
                else:
                    # 左軸
                    if visible_dict.get(key, False):
                        cur_min = min(y)
                        cur_max = max(y)
                        min_y_left = cur_min if min_y_left is None else min(min_y_left, cur_min)
                        max_y_left = cur_max if max_y_left is None else max(max_y_left, cur_max)
                        print(f"  Left axis {key}: visible, range {cur_min:.2f} to {cur_max:.2f}")
                    else:
                        print(f"  Left axis {key}: hidden")
        
        if min_y_left is not None and max_y_left is not None:
            print(f"  Left axis total range: {min_y_left:.2f} to {max_y_left:.2f}")
        if min_y_right is not None and max_y_right is not None:
            print(f"  Right axis total range: {min_y_right:.2f} to {max_y_right:.2f}")

if __name__ == '__main__':
    debug_plotter_logic()
