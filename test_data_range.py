#!/usr/bin/env python3
"""
測試數據範圍問題
"""
import sys
import os

# 設置環境
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLabel
from PySide6.QtCore import QTimer
from plotter import Plotter

class DataRangeTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data Range Test - Check Curve Visibility")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加說明
        info_label = QLabel("這個測試會添加明顯可見的數據範圍，確保曲線能夠顯示")
        layout.addWidget(info_label)
        
        # 添加按鈕
        self.btn_test = QPushButton("開始測試 - 添加可見範圍的數據")
        self.btn_test.clicked.connect(self.start_test)
        layout.addWidget(self.btn_test)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 確保加速度模式
        self.plotter.radio_acc.setChecked(True)
        
        print("Data range test ready.")
        print("This test will add data with obvious ranges to ensure curves are visible.")
        
    def start_test(self):
        print("\n=== Starting Data Range Test ===")
        
        # 清除現有數據
        self.plotter.clear()
        
        # 添加一系列明顯可見的數據
        print("Adding data with obvious ranges...")
        
        for i in range(50):
            # 創建明顯可見的數據範圍
            test_data = {
                'ax': 16384 + i * 200,    # 從1g增加到約1.6g
                'ay': 16384 - i * 100,    # 從1g減少到約0.7g  
                'az': 16384 + (i % 10) * 500,  # 在1g到1.15g之間波動
            }
            
            print(f"Data point {i+1}: ax={test_data['ax']}, ay={test_data['ay']}, az={test_data['az']}")
            
            # 計算轉換後的值
            ax_converted = test_data['ax'] * 16.0 / 32768.0
            ay_converted = test_data['ay'] * 16.0 / 32768.0
            az_converted = test_data['az'] * 16.0 / 32768.0
            
            if i < 5:  # 前5個點顯示轉換後的值
                print(f"  Converted: ax={ax_converted:.3f}g, ay={ay_converted:.3f}g, az={az_converted:.3f}g")
            
            # 添加到plotter
            self.plotter.add_data(test_data)
        
        print(f"\nTest completed. Added 50 data points.")
        print("Expected ranges:")
        print("  ax: 1.000g to 1.610g")
        print("  ay: 0.695g to 1.000g") 
        print("  az: 1.000g to 1.153g")
        print("\nIf you can't see curves, there's a scaling or visibility issue.")
        
        # 檢查最終狀態
        self.check_final_state()
        
    def check_final_state(self):
        print("\n=== Final State Check ===")
        
        # 檢查數據緩衝區
        for key in ['ax', 'ay', 'az']:
            buffer = self.plotter.data_buffers.get(key, [])
            if len(buffer) > 0:
                min_val = min(buffer)
                max_val = max(buffer)
                print(f"{key} buffer: {len(buffer)} points, range {min_val:.3f} to {max_val:.3f}")
            else:
                print(f"{key} buffer: empty")
        
        # 檢查可見性
        print("\nVisibility check:")
        for key in ['ax', 'ay', 'az']:
            visible = self.plotter.visible_dict.get(key, False)
            checkbox = self.plotter.checkbox_map[key].isChecked()
            line_visible = self.plotter.lines[key].isVisible()
            print(f"{key}: visible_dict={visible}, checkbox={checkbox}, line_visible={line_visible}")
        
        # 檢查曲線數據
        print("\nCurve data check:")
        for key in ['ax', 'ay', 'az']:
            line = self.plotter.lines[key]
            if hasattr(line, 'xData') and line.xData is not None:
                x_len = len(line.xData)
                y_len = len(line.yData) if line.yData is not None else 0
                if y_len > 0:
                    y_min = min(line.yData)
                    y_max = max(line.yData)
                    print(f"{key} curve: {x_len} x-points, {y_len} y-points, y-range {y_min:.3f} to {y_max:.3f}")
                else:
                    print(f"{key} curve: {x_len} x-points, no y-data")
            else:
                print(f"{key} curve: no data")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = DataRangeTest()
    window.show()
    sys.exit(app.exec())
