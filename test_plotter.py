#!/usr/bin/env python3
"""
測試plotter圖表功能的獨立腳本
"""
import sys
import os
import time
import math

# 設置pyqtgraph使用PySide6
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer
from plotter import Plotter

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Plotter Test")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 創建定時器來模擬數據
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_test_data)
        self.timer.start(50)  # 20 FPS
        
        self.counter = 0
        
    def generate_test_data(self):
        """生成測試數據"""
        self.counter += 1
        t = self.counter * 0.1
        
        # 生成模擬的IMU數據
        test_data = {
            'ax': int(math.sin(t) * 5000 + 16384),  # 模擬加速度數據
            'ay': int(math.cos(t) * 3000 + 16384),
            'az': int(math.sin(t * 0.5) * 2000 + 16384),
            'gx': int(math.sin(t * 2) * 10000),  # 模擬陀螺儀數據
            'gy': int(math.cos(t * 1.5) * 8000),
            'gz': int(math.sin(t * 0.8) * 6000),
            'roll': int(math.sin(t * 0.3) * 15000),  # 模擬歐拉角數據
            'pitch': int(math.cos(t * 0.4) * 12000),
            'yaw': int(math.sin(t * 0.2) * 18000)
        }
        
        # 添加數據到plotter
        self.plotter.add_data(test_data)
        
        # 每100次打印一次調試信息
        if self.counter % 100 == 0:
            print(f"Generated {self.counter} data points")

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("Test window created. You should see:")
    print("1. A plot window with checkboxes for different data types")
    print("2. Real-time animated curves (ax, ay, az should be visible by default)")
    print("3. Radio buttons to switch between acceleration, gyroscope, and euler angles")
    print("4. Debug messages in the console")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
