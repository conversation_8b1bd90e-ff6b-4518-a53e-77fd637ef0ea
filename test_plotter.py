#!/usr/bin/env python3
"""
測試plotter圖表功能的獨立腳本
"""
import sys
import os
import time
import math

# 設置pyqtgraph使用PySide6
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer
from plotter import Plotter

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Plotter Test")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 創建定時器來模擬數據
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_test_data)
        self.timer.start(50)  # 20 FPS
        
        self.counter = 0
        
    def generate_test_data(self):
        """生成測試數據"""
        self.counter += 1
        t = self.counter * 0.1

        # 生成模擬的IMU數據 - 使用更真實的範圍
        # 加速度：±2g範圍，對應 ±2*16384 = ±32768 的一半
        # 陀螺儀：±500°/s範圍，對應 ±500*32768/2000 = ±8192
        # 歐拉角：±90°範圍，對應 ±90*32768/180 = ±16384
        test_data = {
            'ax': int(math.sin(t) * 8000 + 16384),  # 1g重力 + 變化
            'ay': int(math.cos(t) * 6000),  # ±0.37g變化
            'az': int(math.sin(t * 0.5) * 4000 + 16384),  # 1g重力 + 變化
            'gx': int(math.sin(t * 2) * 4000),  # ±244°/s
            'gy': int(math.cos(t * 1.5) * 3000),  # ±183°/s
            'gz': int(math.sin(t * 0.8) * 2000),  # ±122°/s
            'roll': int(math.sin(t * 0.3) * 8000),  # ±44°
            'pitch': int(math.cos(t * 0.4) * 6000),  # ±33°
            'yaw': int(math.sin(t * 0.2) * 10000)  # ±55°
        }

        # 添加數據到plotter
        self.plotter.add_data(test_data)

        # 每50次打印一次調試信息
        if self.counter % 50 == 0:
            print(f"Generated {self.counter} data points")
            print(f"Sample raw data: ax={test_data['ax']}, roll={test_data['roll']}")
            # 計算轉換後的值
            ax_converted = test_data['ax'] * 16.0 / 32768.0
            roll_converted = test_data['roll'] * 180.0 / 32768.0
            print(f"Converted: ax={ax_converted:.3f}g, roll={roll_converted:.1f}°")

        # 每300次切換模式來測試
        if self.counter % 300 == 50:
            print("\n=== Switching to acceleration mode ===")
            self.plotter.radio_acc.setChecked(True)
        elif self.counter % 300 == 100:
            print("\n=== Switching to gyroscope mode ===")
            self.plotter.radio_gyro.setChecked(True)
        elif self.counter % 300 == 150:
            print("\n=== Switching to euler mode ===")
            self.plotter.radio_euler.setChecked(True)

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("Test window created. You should see:")
    print("1. A plot window with checkboxes for different data types")
    print("2. Real-time animated curves (ax, ay, az should be visible by default)")
    print("3. Radio buttons to switch between acceleration, gyroscope, and euler angles")
    print("4. Debug messages in the console")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
