#!/usr/bin/env python3
"""
最簡單的測試 - 驗證曲線顯示
"""
import sys
import os

# 設置環境
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout
from PySide6.QtCore import QTimer
from plotter import Plotter

class SimpleTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Simple Curve Test")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 確保加速度模式被選中
        print("Setting acceleration mode...")
        self.plotter.radio_acc.setChecked(True)

        # 檢查初始狀態
        print("Initial state:")
        for key in ['ax', 'ay', 'az']:
            visible = self.plotter.visible_dict.get(key, False)
            checkbox_checked = self.plotter.checkbox_map[key].isChecked()
            print(f"  {key}: visible={visible}, checkbox={checkbox_checked}")
        
        # 創建定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_simple_data)
        self.timer.start(200)  # 5 FPS
        
        self.counter = 0
        print("Simple test started - should see acceleration curves")
        
    def add_simple_data(self):
        """添加簡單的測試數據"""
        self.counter += 1
        
        # 生成簡單的測試數據
        simple_data = {
            'ax': 16384 + self.counter * 100,  # 遞增數據
            'ay': 16384 - self.counter * 50,   # 遞減數據  
            'az': 16384,                       # 固定數據
        }
        
        print(f"Adding data {self.counter}: {simple_data}")
        
        # 添加數據到plotter
        self.plotter.add_data(simple_data)
        
        # 檢查數據緩衝區
        if self.counter % 10 == 0:
            for key in ['ax', 'ay', 'az']:
                buffer_len = len(self.plotter.data_buffers.get(key, []))
                visible = self.plotter.visible_dict.get(key, False)
                print(f"  {key}: buffer_len={buffer_len}, visible={visible}")
        
        # 停止在100個數據點
        if self.counter >= 100:
            self.timer.stop()
            print("Test completed - 100 data points added")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SimpleTest()
    window.show()
    sys.exit(app.exec())
