#!/usr/bin/env python3
"""
快速測試腳本 - 檢查圖表修復
"""
import sys
import os
import math

# 設置環境
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QHBoxLayout
from PySide6.QtCore import QTimer
from plotter import Plotter

class QuickTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Quick Chart Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加控制按鈕
        button_layout = QHBoxLayout()
        self.btn_acc = QPushButton("測試加速度")
        self.btn_gyro = QPushButton("測試角速度")
        self.btn_euler = QPushButton("測試歐拉角")
        self.btn_stop = QPushButton("停止")
        
        button_layout.addWidget(self.btn_acc)
        button_layout.addWidget(self.btn_gyro)
        button_layout.addWidget(self.btn_euler)
        button_layout.addWidget(self.btn_stop)
        layout.addLayout(button_layout)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 連接按鈕事件
        self.btn_acc.clicked.connect(self.test_acceleration)
        self.btn_gyro.clicked.connect(self.test_gyroscope)
        self.btn_euler.clicked.connect(self.test_euler)
        self.btn_stop.clicked.connect(self.stop_test)
        
        # 創建定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_data)
        
        self.counter = 0
        self.current_mode = "acc"
        
        print("Quick test ready. Click buttons to test different modes.")
        
    def test_acceleration(self):
        print("\n=== Testing Acceleration Mode ===")
        self.current_mode = "acc"
        self.plotter.radio_acc.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.timer.start(100)
        
    def test_gyroscope(self):
        print("\n=== Testing Gyroscope Mode ===")
        self.current_mode = "gyro"
        self.plotter.radio_gyro.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.timer.start(100)
        
    def test_euler(self):
        print("\n=== Testing Euler Mode ===")
        self.current_mode = "euler"
        self.plotter.radio_euler.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.timer.start(100)
        
    def stop_test(self):
        print("Test stopped")
        self.timer.stop()
        
    def generate_data(self):
        self.counter += 1
        t = self.counter * 0.1
        
        if self.current_mode == "acc":
            # 只生成加速度數據
            test_data = {
                'ax': int(math.sin(t) * 8000 + 16384),
                'ay': int(math.cos(t) * 6000),
                'az': int(math.sin(t * 0.5) * 4000 + 16384),
            }
        elif self.current_mode == "gyro":
            # 只生成陀螺儀數據
            test_data = {
                'gx': int(math.sin(t * 2) * 4000),
                'gy': int(math.cos(t * 1.5) * 3000),
                'gz': int(math.sin(t * 0.8) * 2000),
            }
        else:  # euler
            # 只生成歐拉角數據
            test_data = {
                'roll': int(math.sin(t * 0.3) * 8000),
                'pitch': int(math.cos(t * 0.4) * 6000),
                'yaw': int(math.sin(t * 0.2) * 10000)
            }
        
        self.plotter.add_data(test_data)
        
        # 每50次打印狀態
        if self.counter % 50 == 0:
            print(f"Generated {self.counter} data points for {self.current_mode} mode")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = QuickTest()
    window.show()
    sys.exit(app.exec())
