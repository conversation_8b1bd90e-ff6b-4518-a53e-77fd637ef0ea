#!/usr/bin/env python3
"""
最小化測試 - 只測試基本的數據添加和顯示
"""
import sys
import os

# 設置環境
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton
from PySide6.QtCore import QTimer
from plotter import Plotter

class MinimalTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Minimal Test - Should See Curves")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加按鈕
        self.btn_add_data = QPushButton("Add Test Data")
        self.btn_add_data.clicked.connect(self.add_test_data)
        layout.addWidget(self.btn_add_data)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        print("Minimal test ready. Click 'Add Test Data' to see curves.")
        print("Initial state check:")
        self.check_state()
        
    def check_state(self):
        """檢查當前狀態"""
        print("=== Current State ===")
        print(f"Visible dict: {self.plotter.visible_dict}")
        
        for key in ['ax', 'ay', 'az']:
            checkbox_checked = self.plotter.checkbox_map[key].isChecked()
            line_visible = self.plotter.lines[key].isVisible()
            buffer_len = len(self.plotter.data_buffers.get(key, []))
            print(f"{key}: checkbox={checkbox_checked}, line_visible={line_visible}, buffer_len={buffer_len}")
        
    def add_test_data(self):
        """手動添加測試數據"""
        print("\n=== Adding Test Data ===")
        
        # 添加一些測試數據點
        for i in range(10):
            test_data = {
                'ax': 16384 + i * 1000,  # 遞增
                'ay': 16384 - i * 500,   # 遞減
                'az': 16384,             # 固定
            }
            
            print(f"Adding data point {i+1}: {test_data}")
            self.plotter.add_data(test_data)
        
        print("Data added. Checking state...")
        self.check_state()
        
        # 檢查實際的曲線數據
        print("\n=== Curve Data Check ===")
        for key in ['ax', 'ay', 'az']:
            line = self.plotter.lines[key]
            if hasattr(line, 'xData') and line.xData is not None:
                x_len = len(line.xData)
                y_len = len(line.yData) if line.yData is not None else 0
                print(f"{key} curve: x_points={x_len}, y_points={y_len}")
                if y_len > 0:
                    print(f"  Y data sample: {line.yData[:3]}...")
            else:
                print(f"{key} curve: No data set")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MinimalTest()
    window.show()
    sys.exit(app.exec())
