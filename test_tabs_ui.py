#!/usr/bin/env python3
# test_tabs_ui.py - 測試頁籤UI的簡單腳本

import sys
from PyQt6.QtWidgets import QApplication
from app_pyqt6 import MainWindow

def main():
    """測試頁籤UI"""
    app = QApplication(sys.argv)
    
    # 創建主視窗
    window = MainWindow()
    window.show()
    
    print("頁籤UI測試啟動")
    print("請檢查以下功能：")
    print("1. 頁籤是否正確顯示（實時監控、數據分析、比較分析、趨勢分析）")
    print("2. 每個頁籤的佈局是否正確")
    print("3. 示例數據是否正確顯示")
    print("4. 按鈕和控件是否正常顯示")
    
    # 運行應用程式
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
