#!/usr/bin/env python3
"""
測試Y軸縮放問題
"""
import sys
import os
import math

# 設置環境
os.environ['PYQTGRAPH_QT_LIB'] = 'PySide6'
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QHBoxLayout, QLabel
from PySide6.QtCore import QTimer
from plotter import Plotter

class ScalingTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Y-Axis Scaling Test")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加狀態標籤
        self.status_label = QLabel("Ready to test Y-axis scaling")
        layout.addWidget(self.status_label)
        
        # 添加控制按鈕
        button_layout = QHBoxLayout()
        self.btn_small_data = QPushButton("小範圍數據 (0.1-0.2)")
        self.btn_medium_data = QPushButton("中範圍數據 (5-10)")
        self.btn_large_data = QPushButton("大範圍數據 (50-100)")
        self.btn_euler_data = QPushButton("歐拉角數據 (-90°到90°)")
        self.btn_stop = QPushButton("停止")
        
        button_layout.addWidget(self.btn_small_data)
        button_layout.addWidget(self.btn_medium_data)
        button_layout.addWidget(self.btn_large_data)
        button_layout.addWidget(self.btn_euler_data)
        button_layout.addWidget(self.btn_stop)
        layout.addLayout(button_layout)
        
        # 創建plotter
        self.plotter = Plotter()
        canvas = self.plotter.get_canvas()
        layout.addWidget(canvas)
        
        # 連接按鈕事件
        self.btn_small_data.clicked.connect(self.test_small_data)
        self.btn_medium_data.clicked.connect(self.test_medium_data)
        self.btn_large_data.clicked.connect(self.test_large_data)
        self.btn_euler_data.clicked.connect(self.test_euler_data)
        self.btn_stop.clicked.connect(self.stop_test)
        
        # 創建定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_data)
        
        self.counter = 0
        self.current_test = None
        
        print("Scaling test ready. Click buttons to test different data ranges.")
        
    def test_small_data(self):
        print("\n=== Testing Small Range Data (0.1-0.2) ===")
        self.current_test = "small"
        self.plotter.radio_acc.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.status_label.setText("Testing small range data - should see curves in 0.1-0.2 range")
        self.timer.start(100)
        
    def test_medium_data(self):
        print("\n=== Testing Medium Range Data (5-10) ===")
        self.current_test = "medium"
        self.plotter.radio_acc.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.status_label.setText("Testing medium range data - should see curves in 5-10 range")
        self.timer.start(100)
        
    def test_large_data(self):
        print("\n=== Testing Large Range Data (50-100) ===")
        self.current_test = "large"
        self.plotter.radio_acc.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.status_label.setText("Testing large range data - should see curves in 50-100 range")
        self.timer.start(100)
        
    def test_euler_data(self):
        print("\n=== Testing Euler Data (-90° to 90°) ===")
        self.current_test = "euler"
        self.plotter.radio_euler.setChecked(True)
        self.plotter.clear()
        self.counter = 0
        self.status_label.setText("Testing euler data - should see curves in -90° to 90° range on right axis")
        self.timer.start(100)
        
    def stop_test(self):
        print("Test stopped")
        self.timer.stop()
        self.status_label.setText("Test stopped")
        
    def generate_data(self):
        self.counter += 1
        t = self.counter * 0.1
        
        if self.current_test == "small":
            # 小範圍數據：轉換後約0.1-0.2
            test_data = {
                'ax': int(2048 + math.sin(t) * 1024),  # 轉換後約 0.1-0.2
                'ay': int(2048 + math.cos(t) * 512),   # 轉換後約 0.075-0.125
                'az': int(2048),                       # 轉換後約 0.1
            }
        elif self.current_test == "medium":
            # 中範圍數據：轉換後約5-10
            test_data = {
                'ax': int(10240 + math.sin(t) * 5120),  # 轉換後約 2.5-7.5
                'ay': int(16384 + math.cos(t) * 8192),  # 轉換後約 4-12
                'az': int(16384),                       # 轉換後約 8
            }
        elif self.current_test == "large":
            # 大範圍數據：轉換後約50-100
            test_data = {
                'ax': int(81920 + math.sin(t) * 40960),  # 轉換後約 20-60
                'ay': int(163840 + math.cos(t) * 81920), # 轉換後約 40-120
                'az': int(163840),                       # 轉換後約 80
            }
        else:  # euler
            # 歐拉角數據：轉換後約-90°到90°
            test_data = {
                'roll': int(math.sin(t * 0.3) * 16384),   # 轉換後約 -90° to 90°
                'pitch': int(math.cos(t * 0.4) * 8192),   # 轉換後約 -45° to 45°
                'yaw': int(math.sin(t * 0.2) * 12288)     # 轉換後約 -67.5° to 67.5°
            }
        
        self.plotter.add_data(test_data)
        
        # 每20次打印狀態
        if self.counter % 20 == 0:
            print(f"Generated {self.counter} data points for {self.current_test} test")
            if self.current_test != "euler":
                # 計算轉換後的值
                for key, value in test_data.items():
                    converted = value * 16.0 / 32768.0
                    print(f"  {key}: raw={value}, converted={converted:.3f}")
            else:
                for key, value in test_data.items():
                    converted = value * 180.0 / 32768.0
                    print(f"  {key}: raw={value}, converted={converted:.1f}°")
        
        # 停止在100個數據點
        if self.counter >= 100:
            self.timer.stop()
            self.status_label.setText(f"{self.current_test} test completed - 100 data points")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ScalingTest()
    window.show()
    sys.exit(app.exec())
